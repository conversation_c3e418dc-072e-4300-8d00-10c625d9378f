

# 智妍需求单链接转化成tapd需求单链接
def zhiyan_story_url_mapping_tapd(strory_url):
    if "tapd.woa.com" in strory_url:
        return strory_url
    # tapd 工作空间id 和 智妍项目id 的映射
    mapping = {
        "9535": "20375472"
    }
    story_list = strory_url.split("?")[0].split("/")
    story_id = story_list[-1]
    project_id = story_list[-4]
    workspace_id = mapping[project_id]
    return "https://tapd.woa.com/tapd_fe/{}/story/detail/{}".format(workspace_id, story_id)
